import { useEffect, useRef, useState, useMemo } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useMaybeRoomContext } from '@livekit/components-react';
import { setupMediaToggle } from '@livekit/components-core';
import { useNoiseSuppressionContext } from '../context/indexContext';
import { useObservableState } from './useObservableState';

export const useNoiseSuppression = () => {
  const room = useMaybeRoomContext();
  const { noiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessorRef = useRef(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isNoiseSuppressionActive, setIsNoiseSuppressionActive] = useState(false);

  // Use the same observer pattern as TrackToggle to get real mic state
  const { enabledObserver } = useMemo(
    () => room ? setupMediaToggle(Track.Source.Microphone, room) : { enabledObserver: null },
    [room]
  );

  // Get the real microphone enabled state (same as TrackToggle)
  const isMicEnabled = useObservableState(enabledObserver, false);



  // Simple effect: Apply/remove noise suppression based on toggle
  useEffect(() => {
    const applyNoiseSuppression = async () => {
      if (!room || room.state !== "connected") return;
      if (isProcessing) return;

      // Get microphone track publication (like in your virtualBackground.js)
      const micTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      if (!micTrackPublication) {
        return;
      }

      const micTrack = micTrackPublication.track;
      if (!micTrack) {
        return;
      }

      // Check if track is in a valid state for processing
      const isTrackLive = micTrack.mediaStreamTrack.readyState === 'live';

      // If mic is muted, just skip processing but keep the toggle state
      if (!isMicEnabled) {
        // Don't change any state - keep noise suppression enabled
        // This way when mic is unmuted, noise suppression will still be active
        return;
      }

      // If track is not live but mic is enabled, we need to wait for the track to become live
      // This happens when unmuting - the track might not be immediately ready
      if (!isTrackLive) {
        return;
      }

      // If states match, nothing to do
      if (noiseSuppressionEnabled === isNoiseSuppressionActive) return;

      setIsProcessing(true);

      try {
        if (noiseSuppressionEnabled) {
          // Enable noise suppression (like your example)
          noiseProcessorRef.current = new NoiseSuppressionProcessor();
          const processedTrack = await noiseProcessorRef.current.startProcessing(micTrack.mediaStreamTrack);
          if (processedTrack) {
            await micTrack.replaceTrack(processedTrack, true);
            setIsNoiseSuppressionActive(true);
          }
        } else {
          // Disable noise suppression - need fresh stream because stopProcessing kills track
          if (noiseProcessorRef.current) {
            await noiseProcessorRef.current.stopProcessing();
            noiseProcessorRef.current = null;
          }

          // Create fresh stream to replace the killed track
          const settings = micTrack.mediaStreamTrack.getSettings();
          const constraints = {
            audio: {
              deviceId: settings.deviceId || 'default',
              echoCancellation: false,
              noiseSuppression: false,
            }
          };

          const stream = await navigator.mediaDevices.getUserMedia(constraints);
          const newTrack = stream.getAudioTracks()[0];
          await micTrack.replaceTrack(newTrack, true);

          setIsNoiseSuppressionActive(false);
        }
      } catch (error) {
        setIsNoiseSuppressionActive(false);
      } finally {
        setIsProcessing(false);
      }
    };

    applyNoiseSuppression();
  }, [room, room?.state, noiseSuppressionEnabled, isProcessing, isNoiseSuppressionActive, isMicEnabled]);



  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      if (noiseProcessorRef.current) {
        try {
          noiseProcessorRef.current.stopProcessing();
        } catch (error) {
          // Ignore cleanup errors
        }
        noiseProcessorRef.current = null;
      }
      setIsProcessing(false);
      setIsNoiseSuppressionActive(false);
    };
  }, []);

  return {
    isNoiseSuppressionActive,
    isProcessing,
  };
};
 
